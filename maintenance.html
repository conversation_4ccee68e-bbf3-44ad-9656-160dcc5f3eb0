<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Julep CRM - Scheduled Maintenance</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 15px;
        }

        .maintenance-container {
            background: white;
            border-radius: 12px;
            padding: 2rem 1.8rem;
            max-width: 580px;
            width: 100%;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid #e9ecef;
        }

        .logo {
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo svg {
            display: block;
        }

        .logo img {
            max-width: 280px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .maintenance-icon {
            margin-bottom: 1rem;
            margin-top: 0.5rem;
        }

        .maintenance-icon svg {
            width: 80px;
            height: 80px;
            color: #22c55e;
            filter: drop-shadow(0 4px 8px rgba(34, 197, 94, 0.2));
            animation: maintenance-pulse 2s ease-in-out infinite;
        }

        @keyframes maintenance-pulse {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
            25% {
                transform: scale(1.05) rotate(-5deg);
                opacity: 0.9;
            }
            50% {
                transform: scale(1.1) rotate(0deg);
                opacity: 0.8;
            }
            75% {
                transform: scale(1.05) rotate(5deg);
                opacity: 0.9;
            }
        }

        h1 {
            font-size: 2.1rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 0.8rem;
            letter-spacing: -0.025em;
            line-height: 1.2;
        }

        .subtitle {
            font-size: 1.1rem;
            color: #4a5568;
            margin-bottom: 1.5rem;
            font-weight: 400;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.5;
        }

        .maintenance-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.2rem 0;
            border: 1px solid #e9ecef;
        }

        .maintenance-details h2 {
            color: #1a202c;
            font-size: 1.15rem;
            margin-bottom: 1rem;
            font-weight: 600;
            letter-spacing: -0.01em;
            text-align: center;
        }

        .time-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 0 0 1rem 0;
            max-width: 450px;
            margin-left: auto;
            margin-right: auto;
        }

        .time-block {
            text-align: center;
            padding: 0;
        }

        .time-block strong {
            display: block;
            color: #6c757d;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .time-block span {
            font-size: 1.1rem;
            font-weight: 600;
            color: #22c55e;
            display: block;
            line-height: 1.2;
        }

        .description {
            font-size: 0.95rem;
            color: #4a5568;
            margin: 0;
            line-height: 1.5;
            font-weight: 400;
            text-align: center;
            max-width: 100%;
        }

        .contact-info {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .contact-info p {
            color: #4a5568;
            font-size: 1rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .contact-info a {
            color: #22c55e;
            text-decoration: none;
            font-weight: 500;
            border-bottom: 1px solid transparent;
            transition: border-color 0.3s ease;
        }

        .contact-info a:hover {
            border-bottom-color: #22c55e;
        }

        .footer {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 400;
        }





        /* Desktop and larger screens */
        @media (min-width: 1024px) {
            .maintenance-container {
                padding: 2rem 2.5rem;
            }
        }

        /* Tablet screens */
        @media (max-width: 1023px) and (min-width: 769px) {
            .maintenance-container {
                padding: 2rem;
            }

            h1 {
                font-size: 1.9rem;
            }
        }

        /* Mobile screens */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .maintenance-container {
                padding: 1.5rem 1.2rem;
                margin: 0;
            }

            h1 {
                font-size: 1.7rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .time-info {
                grid-template-columns: 1fr;
                gap: 1.2rem;
                max-width: 280px;
            }

            .logo svg {
                width: 250px;
                height: 65px;
            }

            .maintenance-icon svg {
                width: 70px;
                height: 70px;
            }

            .maintenance-details {
                padding: 1.2rem;
                margin: 1rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="logo">
            <!-- Julep CRM Logo -->
            <img src="https://images.squarespace-cdn.com/content/v1/621e84847658fc7841dcc30a/c3c91c80-f7d3-4245-8f89-79b510ff6e37/Julep_Gradient_Astral_JungleGreen.png" alt="Julep CRM" style="max-width: 280px; height: auto;">
        </div>

        <div class="maintenance-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
            </svg>
        </div>

        <h1>Julep CRM is down for scheduled maintenance</h1>
        <p class="subtitle">We're improving our infrastructure to serve you better. Thank you for your patience.</p>

        <div class="maintenance-details">
            <h2>Maintenance Schedule</h2>
            <div class="time-info">
                <div class="time-block">
                    <strong>Date</strong>
                    <span>September 12, 2025</span>
                </div>
                <div class="time-block">
                    <strong>Time</strong>
                    <span>5:00 PM - 10:00 PM EST</span>
                </div>
            </div>
            <p class="description">
                <strong style="color: #22c55e;">No action is required.</strong> All your data remains safe and secure.
            </p>
        </div>

        <div class="contact-info">
            <p>If you have any urgent questions, please contact our support team at
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>

        <div class="footer">
            <p>© 2025 Julep CRM. All Rights Reserved.</p>
        </div>
    </div>
</body>
</html>
